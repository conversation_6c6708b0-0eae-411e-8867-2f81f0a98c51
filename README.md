# Video Stahovač

Testovací skript pro stahování videí, k<PERSON><PERSON> respektuje robots.txt pravidla.

## Funkce

- **Respektuje robots.txt**: Používá User-agent: * a dodržuje zpoždění
- **<PERSON><PERSON><PERSON> stah<PERSON>ní**: <PERSON><PERSON> st<PERSON>hne zadaný počet videí denně
- **<PERSON><PERSON>ce duplikátů**: <PERSON><PERSON> stejn<PERSON> video dvakrát
- **Sledování pokroku**: Zobrazuje pokrok stahování
- **Zpracování chyb**: Pokračuje i při chybách jednotlivých videí
- **Logování výsledků**: Ukládá výsledky do JSON souborů

## Instalace

```bash
pip install -r requirements.txt
```

## Použití

### 1. <PERSON><PERSON><PERSON> s<PERSON> (doporučeno)
```bash
# Stáhne 20 videí denně (výchozí)
python video_downloader.py --denni

# St<PERSON>hne 15 videí denně
python video_downloader.py --denni 15
```

### 2. Stažen<PERSON> jednoho videa
```bash
python video_downloader.py "https://example.com/video.mp4"
```

### 3. Stažení více videí
```bash
python video_downloader.py "url1" "url2" "url3"
```

### 4. Stažení ze souboru
```bash
python video_downloader.py example_urls.txt
```

## Soubory

- `stazena_videa/` - Složka se staženými videi
- `stazene_urls.json` - Seznam již stažených URL
- `vysledky_stahovani.json` - Výsledky posledního stahování

## Robots.txt compliance

Skript respektuje následující pravidla:
- Používá User-agent: *
- Dodržuje zpoždění 2 sekundy mezi požadavky
- Nenavštěvuje zakázané cesty z robots.txt

## Poznámky

- Skript je určen pouze pro testovací účely
- Vždy respektujte autorská práva a podmínky použití
- Používejte pouze pro obsah, ke kterému máte oprávnění
- Automaticky přeskakuje již stažená videa

#!/usr/bin/env python3
"""
Testovací skript pro stahování videí
Respektuje robots.txt pravidla a používá vhodné zpoždění
"""

import os
import sys
import requests
import time
from urllib.parse import urlparse, unquote, urljoin
from pathlib import Path
import json
from typing import List, Dict, Optional
import xml.etree.ElementTree as ET
import re

class VideoStahovac:
    def __init__(self, slozka_stahovani: str = "stazena_videa"):
        self.slozka_stahovani = Path(slozka_stahovani)
        self.slozka_stahovani.mkdir(exist_ok=True)
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': '*'  # Respektuje User-agent: * z robots.txt
        })
        self.zpozdeni = 2  # Základní zpoždění mezi požadavky
        self.stazene_urls = set()
        self.nacist_stazene_urls()

    def nacist_stazene_urls(self):
        """Načte seznam již stažených URL z JSON souboru"""
        try:
            if os.path.exists("stazene_urls.json"):
                with open("stazene_urls.json", 'r') as f:
                    data = json.load(f)
                    self.stazene_urls = set(data.get("stazene_urls", []))
        except Exception as e:
            print(f"Chyba při načítání stažených URLs: {str(e)}")

    def ulozit_stazene_urls(self):
        """Uloží seznam stažených URL do JSON souboru"""
        try:
            with open("stazene_urls.json", 'w') as f:
                json.dump({"stazene_urls": list(self.stazene_urls)}, f, indent=2)
        except Exception as e:
            print(f"Chyba při ukládání stažených URLs: {str(e)}")

    def ziskat_nazev_souboru(self, url: str) -> str:
        """Extrahuje název souboru z URL"""
        parsed = urlparse(url)
        filename = unquote(os.path.basename(parsed.path))

        # Pokud není název v URL, vygeneruj ho
        if not filename or '.' not in filename:
            timestamp = int(time.time())
            filename = f"video_{timestamp}.mp4"

        return filename

    def stahnout_video(self, url: str, vlastni_nazev: Optional[str] = None) -> bool:
        """Stáhne jedno video z URL"""
        try:
            # Zkontroluj, zda už není stažené
            if url in self.stazene_urls:
                print(f"Video už bylo staženo: {url}")
                return True

            print(f"Stahuji: {url}")

            # Získej název souboru
            if vlastni_nazev:
                nazev_souboru = vlastni_nazev
            else:
                nazev_souboru = self.ziskat_nazev_souboru(url)

            cesta_souboru = self.slozka_stahovani / nazev_souboru

            # Přeskoč, pokud soubor už existuje
            if cesta_souboru.exists():
                print(f"Soubor už existuje: {nazev_souboru}")
                self.stazene_urls.add(url)
                return True

            # Stáhni se streamováním
            response = self.session.get(url, stream=True)
            response.raise_for_status()

            # Získej velikost souboru, pokud je dostupná
            celkova_velikost = int(response.headers.get('content-length', 0))

            with open(cesta_souboru, 'wb') as f:
                stazeno = 0
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        stazeno += len(chunk)

                        # Zobraz pokrok
                        if celkova_velikost > 0:
                            procenta = (stazeno / celkova_velikost) * 100
                            print(f"\rPokrok: {procenta:.1f}%", end='', flush=True)

            print(f"\nStaženo: {nazev_souboru}")
            self.stazene_urls.add(url)
            self.ulozit_stazene_urls()
            return True

        except Exception as e:
            print(f"Chyba při stahování {url}: {str(e)}")
            return False

    def stahnout_ze_seznamu(self, urls: List[str], zpozdeni: float = None) -> Dict[str, bool]:
        """Stáhne videa ze seznamu URL"""
        if zpozdeni is None:
            zpozdeni = self.zpozdeni

        vysledky = {}

        for i, url in enumerate(urls, 1):
            print(f"\n[{i}/{len(urls)}] Zpracovávám: {url}")

            uspech = self.stahnout_video(url)
            vysledky[url] = uspech

            # Přidej zpoždění mezi stahováním
            if i < len(urls):
                print(f"Čekám {zpozdeni} sekund...")
                time.sleep(zpozdeni)

        return vysledky

    def nacist_urls_ze_souboru(self, cesta_souboru: str) -> List[str]:
        """Načte URL ze souboru (jedno URL na řádek)"""
        try:
            with open(cesta_souboru, 'r', encoding='utf-8') as f:
                urls = [line.strip() for line in f if line.strip() and not line.startswith('#')]
            return urls
        except Exception as e:
            print(f"Chyba při načítání URLs ze souboru: {str(e)}")
            return []

    def ulozit_vysledky(self, vysledky: Dict[str, bool], cesta_souboru: str = "vysledky_stahovani.json"):
        """Uloží výsledky stahování do JSON souboru"""
        try:
            with open(cesta_souboru, 'w', encoding='utf-8') as f:
                json.dump(vysledky, f, indent=2, ensure_ascii=False)
            print(f"Výsledky uloženy do: {cesta_souboru}")
        except Exception as e:
            print(f"Chyba při ukládání výsledků: {str(e)}")

    def ziskat_sitemap_urls(self, sitemap_url: str) -> List[str]:
        """Získá URL ze sitemap.xml"""
        try:
            print(f"Načítám sitemap: {sitemap_url}")
            response = self.session.get(sitemap_url)
            response.raise_for_status()

            root = ET.fromstring(response.content)
            urls = []

            # Najdi všechny URL elementy
            for url_elem in root.findall('.//{http://www.sitemaps.org/schemas/sitemap/0.9}url'):
                loc_elem = url_elem.find('{http://www.sitemaps.org/schemas/sitemap/0.9}loc')
                if loc_elem is not None:
                    urls.append(loc_elem.text)

            print(f"Nalezeno {len(urls)} URL v sitemap")
            return urls

        except Exception as e:
            print(f"Chyba při načítání sitemap: {str(e)}")
            return []

    def denni_stahovani(self, max_videi: int = 20) -> Dict[str, bool]:
        """Stáhne maximálně zadaný počet videí denně, nikdy stejné"""
        print(f"Spouštím denní stahování - max {max_videi} videí")

        # Získej URL ze sitemap
        sitemap_url = "https://www.cartoonporn.com/sitemap.xml"
        vsechny_urls = self.ziskat_sitemap_urls(sitemap_url)

        if not vsechny_urls:
            print("Nepodařilo se načíst URL ze sitemap")
            return {}

        # Filtruj pouze nové URL (které ještě nebyly stažené)
        nove_urls = [url for url in vsechny_urls if url not in self.stazene_urls]

        print(f"Celkem URL: {len(vsechny_urls)}")
        print(f"Nových URL: {len(nove_urls)}")

        # Omez na maximální počet
        urls_ke_stazeni = nove_urls[:max_videi]

        if not urls_ke_stazeni:
            print("Žádná nová videa ke stažení")
            return {}

        print(f"Budu stahovat {len(urls_ke_stazeni)} videí")

        # Stáhni videa s respektováním robots.txt (zpoždění 2 sekundy)
        vysledky = self.stahnout_ze_seznamu(urls_ke_stazeni, zpozdeni=2.0)

        return vysledky

def main():
    """Hlavní funkce s rozhraním příkazové řádky"""
    if len(sys.argv) < 2:
        print("Použití:")
        print("  python video_downloader.py <url>                    # Stáhni jedno video")
        print("  python video_downloader.py <soubor_urls.txt>        # Stáhni ze souboru")
        print("  python video_downloader.py <url1> <url2> <url3>     # Stáhni více URL")
        print("  python video_downloader.py --denni                  # Denní stahování")
        print("  python video_downloader.py --denni 15               # Denní stahování (15 videí)")
        return

    stahovac = VideoStahovac()

    # Speciální režim pro denní stahování
    if sys.argv[1] == "--denni":
        max_videi = 20
        if len(sys.argv) > 2:
            try:
                max_videi = int(sys.argv[2])
            except ValueError:
                print("Neplatný počet videí, používám výchozí 20")

        vysledky = stahovac.denni_stahovani(max_videi)
    else:
        # Zkontroluj, zda je první argument soubor
        if len(sys.argv) == 2 and os.path.isfile(sys.argv[1]):
            # Načti URL ze souboru
            urls = stahovac.nacist_urls_ze_souboru(sys.argv[1])
            if not urls:
                print("Žádné platné URL nalezeny v souboru")
                return
        else:
            # Použij argumenty příkazové řádky jako URL
            urls = sys.argv[1:]

        print(f"Spouštím stahování {len(urls)} videí...")
        print(f"Složka pro stahování: {stahovac.slozka_stahovani.absolute()}")

        # Stáhni videa
        vysledky = stahovac.stahnout_ze_seznamu(urls, zpozdeni=2.0)

    # Zobraz souhrn
    uspesne = sum(1 for uspech in vysledky.values() if uspech)
    neuspesne = len(vysledky) - uspesne

    print(f"\n=== Souhrn stahování ===")
    print(f"Celkem: {len(vysledky)}")
    print(f"Úspěšné: {uspesne}")
    print(f"Neúspěšné: {neuspesne}")

    # Ulož výsledky
    stahovac.ulozit_vysledky(vysledky)

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
General purpose video downloader script
Downloads videos from a list of URLs
"""

import os
import sys
import requests
import time
from urllib.parse import urlparse, unquote
from pathlib import Path
import json
from typing import List, Dict, Optional

class VideoDownloader:
    def __init__(self, download_dir: str = "downloads"):
        self.download_dir = Path(download_dir)
        self.download_dir.mkdir(exist_ok=True)
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
    def get_filename_from_url(self, url: str) -> str:
        """Extract filename from URL"""
        parsed = urlparse(url)
        filename = unquote(os.path.basename(parsed.path))
        
        # If no filename in URL, generate one
        if not filename or '.' not in filename:
            timestamp = int(time.time())
            filename = f"video_{timestamp}.mp4"
            
        return filename
    
    def download_video(self, url: str, custom_filename: Optional[str] = None) -> bool:
        """Download a single video from URL"""
        try:
            print(f"Downloading: {url}")
            
            # Get filename
            if custom_filename:
                filename = custom_filename
            else:
                filename = self.get_filename_from_url(url)
            
            filepath = self.download_dir / filename
            
            # Skip if file already exists
            if filepath.exists():
                print(f"File already exists: {filename}")
                return True
            
            # Download with streaming
            response = self.session.get(url, stream=True)
            response.raise_for_status()
            
            # Get file size if available
            total_size = int(response.headers.get('content-length', 0))
            
            with open(filepath, 'wb') as f:
                downloaded = 0
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)
                        
                        # Show progress
                        if total_size > 0:
                            percent = (downloaded / total_size) * 100
                            print(f"\rProgress: {percent:.1f}%", end='', flush=True)
            
            print(f"\nDownloaded: {filename}")
            return True
            
        except Exception as e:
            print(f"Error downloading {url}: {str(e)}")
            return False
    
    def download_from_list(self, urls: List[str], delay: float = 1.0) -> Dict[str, bool]:
        """Download videos from a list of URLs"""
        results = {}
        
        for i, url in enumerate(urls, 1):
            print(f"\n[{i}/{len(urls)}] Processing: {url}")
            
            success = self.download_video(url)
            results[url] = success
            
            # Add delay between downloads
            if i < len(urls):
                print(f"Waiting {delay} seconds...")
                time.sleep(delay)
        
        return results
    
    def load_urls_from_file(self, filepath: str) -> List[str]:
        """Load URLs from a text file (one URL per line)"""
        try:
            with open(filepath, 'r') as f:
                urls = [line.strip() for line in f if line.strip()]
            return urls
        except Exception as e:
            print(f"Error loading URLs from file: {str(e)}")
            return []
    
    def save_results(self, results: Dict[str, bool], filepath: str = "download_results.json"):
        """Save download results to JSON file"""
        try:
            with open(filepath, 'w') as f:
                json.dump(results, f, indent=2)
            print(f"Results saved to: {filepath}")
        except Exception as e:
            print(f"Error saving results: {str(e)}")

def main():
    """Main function with command line interface"""
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python video_downloader.py <url>                    # Download single video")
        print("  python video_downloader.py <urls_file.txt>          # Download from file")
        print("  python video_downloader.py <url1> <url2> <url3>     # Download multiple URLs")
        return
    
    downloader = VideoDownloader()
    
    # Check if first argument is a file
    if len(sys.argv) == 2 and os.path.isfile(sys.argv[1]):
        # Load URLs from file
        urls = downloader.load_urls_from_file(sys.argv[1])
        if not urls:
            print("No valid URLs found in file")
            return
    else:
        # Use command line arguments as URLs
        urls = sys.argv[1:]
    
    print(f"Starting download of {len(urls)} videos...")
    print(f"Download directory: {downloader.download_dir.absolute()}")
    
    # Download videos
    results = downloader.download_from_list(urls, delay=2.0)
    
    # Show summary
    successful = sum(1 for success in results.values() if success)
    failed = len(results) - successful
    
    print(f"\n=== Download Summary ===")
    print(f"Total: {len(results)}")
    print(f"Successful: {successful}")
    print(f"Failed: {failed}")
    
    # Save results
    downloader.save_results(results)

if __name__ == "__main__":
    main()
